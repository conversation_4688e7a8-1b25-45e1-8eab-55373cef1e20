AVS Tolerant
=====================================================

The service providing the check with belonging user to the PEP (politically exposed persons) list or the sanctions list.

config.json
-----------------------------------------------------
```json
"tolerant": {
  "recheckSchedule": "0 * * * * *"
}
```

Together with the **recheck after** service parameter that value controls the automatic recheck run.
According to the cron schedule, set at the `recheckSchedule` parameter, automatical recheck is run for the users who were checked previously more than the set amount seconds ago.

Methods:
------------------------------------------------------
**check(userId)** - Starting the checking process. The method is equal for both services.

**Parameters:**

- _{string}_ **userId**

Response:
--------------------------------------------------------------------------------
```json
{
  "success": boolean,
  "error": string          //Optional
}
```