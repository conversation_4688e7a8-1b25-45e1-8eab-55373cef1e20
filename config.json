{"encryption": {"bcryptSaltRounds": 10, "cryptoHash": "sha256", "keysPath": "./__keys", "rsa": {"length": 512}, "jwt": {"algorithm": "RS256"}}, "user": {"passwordRegex": "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{8,255}$", "validations": ["email", "birthday", "age", "iban", "phone", "zip"], "requiredFields": {"tolerant.pep": ["firstName", "lastName", "birthday", "city", "zip", "street"], "tolerant.sanctions": ["firstName", "lastName", "birthday", "city", "zip", "street"]}}, "db": {"client": "mysql", "connection": {"host": "127.0.0.1", "user": "root", "password": "password", "database": "avs", "charset": "utf8"}}, "serviceBus": {"reconnectTimeout": 1000}, "serviceController": {"port": 5004}, "redis": {"port": 6379, "host": "127.0.0.1", "options": {}}, "file": {"uploadPath": "../../uploads/"}, "templating": {"lang": "en", "allLanguages": ["en", "de"]}, "locker": {"path": "__locks", "options": {"stale": 10000, "wait": 10000, "pollPeriod": 0}}, "tolerant": {"recheckSchedule": "0 * * * * *"}, "timezone": "Europe/Kiev"}