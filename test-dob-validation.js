import { TolerantPepService } from './src/tolerant-service/tolerant-pep-service.js'
import moment from 'moment'

// Mock User and Profile classes for testing
class MockProfile {
  constructor(data) {
    this.data = data
  }
  
  get(field) {
    return this.data[field]
  }
}

class MockUser {
  constructor(data) {
    this.data = data
    this.profile = new MockProfile(data.profile || {})
  }
  
  get(field) {
    return this.data[field]
  }
  
  related(relation) {
    if (relation === 'profile') {
      return this.profile
    }
    return null
  }
}

// Create a test instance
const service = new TolerantPepService()

// Test cases
const testCases = [
  {
    name: 'Valid DOB - Adult',
    birthday: '1990-05-15',
    expectedValid: true
  },
  {
    name: 'Valid DOB - Elderly',
    birthday: '1940-01-01',
    expectedValid: true
  },
  {
    name: 'Invalid DOB - Future date',
    birthday: moment().add(1, 'day').format('YYYY-MM-DD'),
    expectedValid: false,
    expectedError: 'DOB cannot be in the future'
  },
  {
    name: 'Invalid DOB - Too old (over 150 years)',
    birthday: moment().subtract(151, 'years').format('YYYY-MM-DD'),
    expectedValid: false,
    expectedError: 'DOB indicates age greater than 150 years'
  },
  {
    name: 'Invalid DOB - Too young (under 16)',
    birthday: moment().subtract(15, 'years').format('YYYY-MM-DD'),
    expectedValid: false,
    expectedError: 'DOB indicates person is under 16 years old'
  },
  {
    name: 'Invalid DOB - Malformed date',
    birthday: 'invalid-date',
    expectedValid: false,
    expectedError: 'DOB is not a valid date format'
  },
  {
    name: 'Invalid DOB - Empty string',
    birthday: '',
    expectedValid: false,
    expectedError: 'DOB is required but not provided'
  },
  {
    name: 'Invalid DOB - Null',
    birthday: null,
    expectedValid: false,
    expectedError: 'DOB is required but not provided'
  }
]

console.log('Testing DOB Validation...\n')

let passedTests = 0
let totalTests = testCases.length

for (const testCase of testCases) {
  console.log(`Testing: ${testCase.name}`)
  console.log(`  Birthday: ${testCase.birthday}`)
  
  const mockUser = new MockUser({
    id: 'test-user-123',
    partnerId: 'test-partner',
    profile: {
      birthday: testCase.birthday
    }
  })
  
  try {
    const validationResult = service.validateDOB(mockUser)
    
    if (testCase.expectedValid) {
      if (validationResult === null) {
        console.log('  ✅ PASS - DOB is valid as expected')
        passedTests++
      } else {
        console.log(`  ❌ FAIL - Expected valid DOB but got error: ${validationResult.error}`)
      }
    } else {
      if (validationResult !== null && validationResult.error.includes(testCase.expectedError)) {
        console.log(`  ✅ PASS - Got expected error: ${validationResult.error}`)
        passedTests++
      } else if (validationResult === null) {
        console.log(`  ❌ FAIL - Expected error "${testCase.expectedError}" but DOB was considered valid`)
      } else {
        console.log(`  ❌ FAIL - Expected error "${testCase.expectedError}" but got: ${validationResult.error}`)
      }
    }
  } catch (error) {
    console.log(`  ❌ FAIL - Unexpected exception: ${error.message}`)
  }
  
  console.log('')
}

console.log(`\nTest Results: ${passedTests}/${totalTests} tests passed`)

if (passedTests === totalTests) {
  console.log('🎉 All tests passed!')
  process.exit(0)
} else {
  console.log('❌ Some tests failed')
  process.exit(1)
}
