import { MicroService, ServiceStatus, serviceBus } from 'avs-core/service/index.js'
import { templateProcessor } from 'avs-core/templating/index.js'
import { User, UserMessage } from 'avs-core/user/index.js'
import { configProxy as config } from 'avs-core/config/index.js'
import { fetch } from 'avs-core/fetch/index.js'
import { cron } from 'avs-core/cron/index.js'

const obj2str = obj => {
  return (
    '{\n' +
    Object.keys(obj)
      .map(k => {
        return `  ${k}: ${typeof obj[k] === 'object' ? obj2str(obj[k]) : obj[k]}`
      })
      .join(',\n') +
    '\n}'
  )
}

export class TolerantService extends MicroService {
  async run() {
    await super.run()

    cron(config.tolerant?.recheckSchedule, this.recheckAll.bind(this))
  }

  async check(userId) {
    let user

    try {
      user = await new User({ id: userId }).fetch({ withRelated: ['profile'] })

      const emptyFields = await this.checkFields(user)
      await this.preCondition(user, ServiceStatus.STARTED)

      if (emptyFields) {
        await this.setStatus(user, 'check', ServiceStatus.STARTED, null, emptyFields, null, false)

        return { success: false, fields: emptyFields }
      }

      await this.setStatus(user, 'check', ServiceStatus.STARTED)

      const matchResults = await this.sendRequest(user)

      if (matchResults.length) await this.onMatch(user, matchResults)

      const status = matchResults.length ? ServiceStatus.FAILURE : ServiceStatus.SUCCESS
      await this.setStatus(user, 'check', status)

      return { success: true }
    } catch (error) {
      this.setStatus(user, 'check', ServiceStatus.DOWN, error)

      return { success: false, error: error.toString() }
    }
  }

  /**
   * Send request to tolerant
   * @param {string} partnerId
   * @param {User} user
   */
  async sendRequest(user) {
    const params = await this.getParams(user.get('partnerId'), [
      'block',
      'supportEmail',
      'url',
      'project',
      'profile',
      'user',
      'password',
      'threshold'
    ])

    const requestConfig = {
      uri: `${params.url}${params.project}/${params.profile}`,
      qs: {
        ...this.getQuery(user.related('profile').toJSON()),
        user: params.user,
        password: params.password
      }
    }

    await UserMessage.post({
      user,
      service: this.name,
      template: 'message.info.service.request',
      data: {
        data: JSON.stringify({
          ...requestConfig,
          qs: {
            ...requestConfig.qs,
            user: '******',
            password: '******'
          }
        })
      }
    })

    const searchParams = new URLSearchParams(requestConfig.qs)
    const res = await fetch(`${requestConfig.uri}?${searchParams}`)
    const json = await res.json()

    this.logger.devInfo(json)

    await UserMessage.post({
      user,
      service: this.name,
      template: 'message.info.service.response',
      data: {
        data: JSON.stringify(json)
      }
    })

    if (json.errorMsg !== 'OK') {
      throw json.errorMsg
    }

    return this.checkThreshold(json.results, params.threshold)
  }

  /**
   *
   * @param {string} partnerId
   * @param {User} user
   * @param {array} matchResults
   */
  async onMatch(user, matchResults) {
    const partnerId = user.get('partnerId')
    const params = await this.getParams(partnerId, ['block', 'supportEmail'])
    const score = matchResults[0]['match.Score']
    const originalData = obj2str(matchResults[0]['match.OriginalData'])

    await UserMessage.post({
      user,
      service: this.name,
      template: 'message.error.tolerant.verification',
      data: { score, originalData }
    })

    const subject = templateProcessor.compile(partnerId, 'subject.email.tolerant')
    const body = templateProcessor.compile(partnerId, 'email.tolerant', {
      partnerId,
      userId: user.get('id'),
      score,
      originalData
    })

    serviceBus
      .execute('emailer', 'send', [
        {
          to: params.supportEmail,
          subject,
          isHtml: true,
          body
        }
      ])
      .catch(err => this.logger.error(err))

    if (params.block) await user.block(this.name)
  }

  /**
   * Get query object of user profile
   *
   * @param {object} profile
   * @returns {object}
   */
  getQuery() {
    throw {
      type: 'INTERNAL',
      message: 'error.not_implemented',
      data: { name: this._name, method: 'getQuery' }
    }
  }

  /**
   * Check treshold and filter results
   *
   * @param {array} results
   * @param {array} threshold
   * @returns {array}
   */
  checkThreshold(results, threshold) {
    this.logger.devInfo('check threshold', results, threshold)

    return results.filter(result => {
      const { First_Name, Last_Name, DOB } = result['match.SingleScores']

      return [First_Name, Last_Name, DOB].every((score, i) => {
        return +score >= +(threshold[i] || 0)
      })
    })
  }
}
