import { TolerantService } from './tolerant-service.js'

import moment from 'moment'

export class TolerantSanctionsService extends TolerantService {
  constructor() {
    super(false, 'tolerant.sanctions')
  }

  /**
   * Get query object of user profile
   *
   * @param {object} profile
   * @returns {object}
   */
  getQuery(profile) {
    return {
      First_Name: profile.firstName,
      Last_Name: profile.lastName,
      ExPEPs: '',
      DOB: moment(profile.birthday).format('DD.MM.YYYY'),
      Country: 'Germany',
      POB: profile.placeOfBirth,
      Street: `${profile.street} ${profile.houseNumber || ''}`.trim(),
      City: profile.city,
      resultMode: 4,
      PID: profile.userId,
      reqId: 14,
      maxResultCount: 10
    }
  }
}
