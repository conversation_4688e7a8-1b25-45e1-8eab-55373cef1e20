import { TolerantService } from './tolerant-service.js'

import moment from 'moment'

export class TolerantPepService extends TolerantService {
  constructor() {
    super(false, 'tolerant.pep')
  }

  /**
   * Get query object of user profile
   *
   * @param {object} profile
   * @returns {object}
   */
  getQuery(profile) {
    return {
      First_Name: profile.firstName,
      Last_Name: profile.lastName,
      ExPEPs: '',
      DOB: moment(profile.birthday).format('DD.MM.YYYY'),
      Country: 'Germany',
      POB: profile.placeOfBirth,
      Address: `${profile.street} ${profile.houseNumber || ''}`.trim(),
      resultMode: 4,
      PID: profile.userId,
      reqId: 14,
      maxResultCount: 10
    }
  }
}
