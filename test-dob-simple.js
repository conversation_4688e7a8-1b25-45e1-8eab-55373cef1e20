// Simple DOB validation test using existing dependencies
import moment from 'moment'

// Mock the minimal parts we need
class MockProfile {
  constructor(birthday) {
    this.birthday = birthday
  }
  get(field) {
    return this.birthday
  }
}

class MockUser {
  constructor(birthday) {
    this.profile = new MockProfile(birthday)
  }
  get(field) {
    return field === 'id' ? 'test-123' : 'test-partner'
  }
  related(relation) {
    return this.profile
  }
}

// Copy the validateDOB logic here for testing
function validateDOB(user) {
  try {
    const profile = user.related('profile')
    const birthday = profile.get('birthday')

    if (!birthday) {
      return {
        field: 'birthday',
        error: 'DOB is required but not provided',
        value: birthday
      }
    }

    const dobMoment = moment(birthday)
    
    if (!dobMoment.isValid()) {
      return {
        field: 'birthday',
        error: 'DOB is not a valid date format',
        value: birthday
      }
    }

    const now = moment()

    if (dobMoment.isAfter(now)) {
      return {
        field: 'birthday',
        error: 'DOB cannot be in the future',
        value: birthday,
        formattedValue: dobMoment.format('DD.MM.YYYY')
      }
    }

    const maxAge = moment().subtract(150, 'years')
    if (dobMoment.isBefore(maxAge)) {
      return {
        field: 'birthday',
        error: 'DOB indicates age greater than 150 years, which is not realistic',
        value: birthday,
        formattedValue: dobMoment.format('DD.MM.YYYY')
      }
    }

    const minAge = moment().subtract(16, 'years')
    if (dobMoment.isAfter(minAge)) {
      return {
        field: 'birthday',
        error: 'DOB indicates person is under 16 years old',
        value: birthday,
        formattedValue: dobMoment.format('DD.MM.YYYY')
      }
    }

    return null
  } catch (error) {
    return {
      field: 'birthday',
      error: 'Unexpected error occurred during DOB validation',
      value: user?.related('profile')?.get('birthday'),
      originalError: error.toString()
    }
  }
}

// Test cases
const tests = [
  { name: 'Valid adult', birthday: '1990-05-15', shouldPass: true },
  { name: 'Future date', birthday: moment().add(1, 'day').format('YYYY-MM-DD'), shouldPass: false },
  { name: 'Too old', birthday: '1850-01-01', shouldPass: false },
  { name: 'Too young', birthday: moment().subtract(10, 'years').format('YYYY-MM-DD'), shouldPass: false },
  { name: 'Invalid format', birthday: 'invalid-date', shouldPass: false },
  { name: 'Empty', birthday: '', shouldPass: false }
]

console.log('Testing DOB Validation:\n')

tests.forEach(test => {
  const user = new MockUser(test.birthday)
  const result = validateDOB(user)
  const passed = test.shouldPass ? result === null : result !== null
  
  console.log(`${passed ? '✅' : '❌'} ${test.name}: ${test.birthday}`)
  if (result) {
    console.log(`   Error: ${result.error}`)
  }
})
