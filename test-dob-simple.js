// Simple DOB validation test using native JavaScript Date
// (No external dependencies needed)

// Mock the minimal parts we need
class MockProfile {
  constructor(birthday) {
    this.birthday = birthday
  }
  get(field) {
    return this.birthday
  }
}

class MockUser {
  constructor(birthday) {
    this.profile = new MockProfile(birthday)
  }
  get(field) {
    return field === 'id' ? 'test-123' : 'test-partner'
  }
  related(relation) {
    return this.profile
  }
}

// Helper functions to replace moment.js functionality
function isValidDate(dateString) {
  const date = new Date(dateString)
  return date instanceof Date && !isNaN(date.getTime())
}

function formatDate(date) {
  const d = new Date(date)
  const day = String(d.getDate()).padStart(2, '0')
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const year = d.getFullYear()
  return `${day}.${month}.${year}`
}

function getYearsAgo(years) {
  const date = new Date()
  date.setFullYear(date.getFullYear() - years)
  return date
}

// Copy the validateDOB logic here for testing (using native Date instead of moment)
function validateDOB(user) {
  try {
    const profile = user.related('profile')
    const birthday = profile.get('birthday')

    if (!birthday) {
      return {
        field: 'birthday',
        error: 'DOB is required but not provided',
        value: birthday
      }
    }

    if (!isValidDate(birthday)) {
      return {
        field: 'birthday',
        error: 'DOB is not a valid date format',
        value: birthday
      }
    }

    const dobDate = new Date(birthday)
    const now = new Date()

    if (dobDate > now) {
      return {
        field: 'birthday',
        error: 'DOB cannot be in the future',
        value: birthday,
        formattedValue: formatDate(dobDate)
      }
    }

    const maxAge = getYearsAgo(150)
    if (dobDate < maxAge) {
      return {
        field: 'birthday',
        error: 'DOB indicates age greater than 150 years, which is not realistic',
        value: birthday,
        formattedValue: formatDate(dobDate)
      }
    }

    const minAge = getYearsAgo(16)
    if (dobDate > minAge) {
      return {
        field: 'birthday',
        error: 'DOB indicates person is under 16 years old',
        value: birthday,
        formattedValue: formatDate(dobDate)
      }
    }

    return null
  } catch (error) {
    return {
      field: 'birthday',
      error: 'Unexpected error occurred during DOB validation',
      value: user?.related('profile')?.get('birthday'),
      originalError: error.toString()
    }
  }
}

// Test cases
const tomorrow = new Date()
tomorrow.setDate(tomorrow.getDate() + 1)

const tenYearsAgo = new Date()
tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10)

const tests = [
  { name: 'Valid adult', birthday: '1990-05-15', shouldPass: true },
  { name: 'Future date', birthday: tomorrow.toISOString().split('T')[0], shouldPass: false },
  { name: 'Too old', birthday: '1850-01-01', shouldPass: false },
  { name: 'Too young', birthday: tenYearsAgo.toISOString().split('T')[0], shouldPass: false },
  { name: 'Invalid format', birthday: 'invalid-date', shouldPass: false },
  { name: 'Empty', birthday: '', shouldPass: false },
  { name: 'Invalid DD.MM.YYYY format', birthday: '00.00.1988', shouldPass: false }
]

console.log('Testing DOB Validation:\n')

tests.forEach(test => {
  const user = new MockUser(test.birthday)
  const result = validateDOB(user)
  const passed = test.shouldPass ? result === null : result !== null
  
  console.log(`${passed ? '✅' : '❌'} ${test.name}: ${test.birthday}`)
  if (result) {
    console.log(`   Error: ${result.error}`)
  }
})
